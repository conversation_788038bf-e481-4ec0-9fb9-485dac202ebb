# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

from typing import Optional, Union
from urllib.parse import urlencode
from . import _api_module
from . import _common
from . import types
from ._api_client import ApiClient
from ._common import get_value_by_path as getv
from ._common import set_value_by_path as setv


def _GetOperationParameters_to_mldev(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['operation_name']) is not None:
    setv(
        to_object,
        ['_url', 'operationName'],
        getv(from_object, ['operation_name']),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _GetOperationParameters_to_vertex(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['operation_name']) is not None:
    setv(
        to_object,
        ['_url', 'operationName'],
        getv(from_object, ['operation_name']),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _FetchPredictOperationParameters_to_mldev(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['operation_name']) is not None:
    raise ValueError('operation_name parameter is not supported in Gemini API.')

  if getv(from_object, ['resource_name']) is not None:
    raise ValueError('resource_name parameter is not supported in Gemini API.')

  if getv(from_object, ['config']) is not None:
    raise ValueError('config parameter is not supported in Gemini API.')

  return to_object


def _FetchPredictOperationParameters_to_vertex(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['operation_name']) is not None:
    setv(to_object, ['operationName'], getv(from_object, ['operation_name']))

  if getv(from_object, ['resource_name']) is not None:
    setv(
        to_object,
        ['_url', 'resourceName'],
        getv(from_object, ['resource_name']),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _Operation_from_mldev(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['metadata']) is not None:
    setv(to_object, ['metadata'], getv(from_object, ['metadata']))

  if getv(from_object, ['done']) is not None:
    setv(to_object, ['done'], getv(from_object, ['done']))

  if getv(from_object, ['error']) is not None:
    setv(to_object, ['error'], getv(from_object, ['error']))

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  return to_object


def _Operation_from_vertex(
    api_client: ApiClient,
    from_object: Union[dict, object],
    parent_object: dict = None,
) -> dict:
  to_object = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['metadata']) is not None:
    setv(to_object, ['metadata'], getv(from_object, ['metadata']))

  if getv(from_object, ['done']) is not None:
    setv(to_object, ['done'], getv(from_object, ['done']))

  if getv(from_object, ['error']) is not None:
    setv(to_object, ['error'], getv(from_object, ['error']))

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  return to_object


class _operations(_api_module.BaseModule):

  def _get_operation(
      self,
      *,
      operation_name: str,
      config: Optional[types.GetOperationConfigOrDict] = None,
  ) -> types.Operation:
    parameter_model = types._GetOperationParameters(
        operation_name=operation_name,
        config=config,
    )

    if self._api_client.vertexai:
      request_dict = _GetOperationParameters_to_vertex(
          self._api_client, parameter_model
      )
      path = '{operationName}'.format_map(request_dict.get('_url'))
    else:
      request_dict = _GetOperationParameters_to_mldev(
          self._api_client, parameter_model
      )
      path = '{operationName}'.format_map(request_dict.get('_url'))
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options = None
    if isinstance(config, dict):
      http_options = config.get('http_options', None)
    elif hasattr(config, 'http_options'):
      http_options = config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response_dict = self._api_client.request(
        'get', path, request_dict, http_options
    )

    if self._api_client.vertexai:
      response_dict = _Operation_from_vertex(self._api_client, response_dict)
    else:
      response_dict = _Operation_from_mldev(self._api_client, response_dict)

    return_value = types.Operation._from_response(
        response=response_dict, kwargs=parameter_model
    )
    self._api_client._verify_response(return_value)
    return return_value

  def _fetch_predict_operation(
      self,
      *,
      operation_name: str,
      resource_name: str,
      config: Optional[types.FetchPredictOperationConfigOrDict] = None,
  ) -> types.Operation:
    parameter_model = types._FetchPredictOperationParameters(
        operation_name=operation_name,
        resource_name=resource_name,
        config=config,
    )

    if not self._api_client.vertexai:
      raise ValueError('This method is only supported in the Vertex AI client.')
    else:
      request_dict = _FetchPredictOperationParameters_to_vertex(
          self._api_client, parameter_model
      )
      path = '{resourceName}:fetchPredictOperation'.format_map(
          request_dict.get('_url')
      )

    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options = None
    if isinstance(config, dict):
      http_options = config.get('http_options', None)
    elif hasattr(config, 'http_options'):
      http_options = config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )

    if self._api_client.vertexai:
      response_dict = _Operation_from_vertex(self._api_client, response_dict)
    else:
      response_dict = _Operation_from_mldev(self._api_client, response_dict)

    return_value = types.Operation._from_response(
        response=response_dict, kwargs=parameter_model
    )
    self._api_client._verify_response(return_value)
    return return_value


class Async_operations(_api_module.BaseModule):

  async def _get_operation(
      self,
      *,
      operation_name: str,
      config: Optional[types.GetOperationConfigOrDict] = None,
  ) -> types.Operation:
    parameter_model = types._GetOperationParameters(
        operation_name=operation_name,
        config=config,
    )

    if self._api_client.vertexai:
      request_dict = _GetOperationParameters_to_vertex(
          self._api_client, parameter_model
      )
      path = '{operationName}'.format_map(request_dict.get('_url'))
    else:
      request_dict = _GetOperationParameters_to_mldev(
          self._api_client, parameter_model
      )
      path = '{operationName}'.format_map(request_dict.get('_url'))
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options = None
    if isinstance(config, dict):
      http_options = config.get('http_options', None)
    elif hasattr(config, 'http_options'):
      http_options = config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response_dict = await self._api_client.async_request(
        'get', path, request_dict, http_options
    )

    if self._api_client.vertexai:
      response_dict = _Operation_from_vertex(self._api_client, response_dict)
    else:
      response_dict = _Operation_from_mldev(self._api_client, response_dict)

    return_value = types.Operation._from_response(
        response=response_dict, kwargs=parameter_model
    )
    self._api_client._verify_response(return_value)
    return return_value

  async def _fetch_predict_operation(
      self,
      *,
      operation_name: str,
      resource_name: str,
      config: Optional[types.FetchPredictOperationConfigOrDict] = None,
  ) -> types.Operation:
    parameter_model = types._FetchPredictOperationParameters(
        operation_name=operation_name,
        resource_name=resource_name,
        config=config,
    )

    if not self._api_client.vertexai:
      raise ValueError('This method is only supported in the Vertex AI client.')
    else:
      request_dict = _FetchPredictOperationParameters_to_vertex(
          self._api_client, parameter_model
      )
      path = '{resourceName}:fetchPredictOperation'.format_map(
          request_dict.get('_url')
      )

    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options = None
    if isinstance(config, dict):
      http_options = config.get('http_options', None)
    elif hasattr(config, 'http_options'):
      http_options = config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response_dict = await self._api_client.async_request(
        'post', path, request_dict, http_options
    )

    if self._api_client.vertexai:
      response_dict = _Operation_from_vertex(self._api_client, response_dict)
    else:
      response_dict = _Operation_from_mldev(self._api_client, response_dict)

    return_value = types.Operation._from_response(
        response=response_dict, kwargs=parameter_model
    )
    self._api_client._verify_response(return_value)
    return return_value
