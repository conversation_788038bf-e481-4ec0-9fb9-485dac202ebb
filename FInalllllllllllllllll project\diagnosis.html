<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ابدأ التشخيص - مساعد طبي ذكي</title>
    <meta name="description" content="اوصف أعراضك واحصل على تحليل وتشخيص بالذكاء الاصطناعي">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="diagnosis.html">التشخيص</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.html">حجز موعد</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">اتصل بنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="About.Html">عن المشروع</a>
                    </li>
                    <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
                        <button class="btn btn-primary rounded-pill px-4" id="loginBtn">تسجيل الدخول</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-4 shadow">
                <div class="modal-header p-4 pb-0 border-bottom-0">
                    <ul class="nav nav-tabs nav-fill w-100" id="authTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-tab-pane" type="button" role="tab" aria-controls="login-tab-pane" aria-selected="true">تسجيل الدخول</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register-tab-pane" type="button" role="tab" aria-controls="register-tab-pane" aria-selected="false">إنشاء حساب</button>
                        </li>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 pt-0">
                    <div class="tab-content" id="authTabContent">
                        <!-- Login Form -->
                        <div class="tab-pane fade show active" id="login-tab-pane" role="tabpanel" aria-labelledby="login-tab" tabindex="0">
                            <form id="loginForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="loginEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="loginEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">تسجيل الدخول</button>
                            </form>
                        </div>
                        <!-- Register Form -->
                        <div class="tab-pane fade" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                            <form id="registerForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="registerName" class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="registerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">إنشاء حساب</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="diagnosis-form-container p-4 p-md-5">
                        <div class="text-center mb-4">
                            <h1 class="mb-2">ابدأ تشخيصك</h1>
                            <p class="text-muted">اوصف أعراضك بالتفصيل للحصول على أدق تحليل</p>
                        </div>

                        <form id="symptomForm" action="results.html" method="GET">
                            <div class="mb-4">
                                <label for="symptoms" class="form-label fw-medium">اوصف أعراضك:</label>
                                <textarea
                                    id="symptoms"
                                    name="symptoms"
                                    class="form-control"
                                    rows="6"
                                    placeholder="مثال: أعاني من صداع وحمى منذ يومين..."
                                    required
                                ></textarea>
                            </div>
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label for="age" class="form-label fw-medium">العمر:</label>
                                    <input type="number" id="age" name="age" class="form-control" min="0" max="120">
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label fw-medium">الجنس:</label>
                                    <select id="gender" name="gender" class="form-select">
                                        <option value="">أفضل عدم الإفصاح</option>
                                        <option value="male">ذكر</option>
                                        <option value="female">أنثى</option>
                                        <option value="other">آخر</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-sm-row justify-content-between gap-3">
                                <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    رجوع
                                </button>
                                <button type="submit" class="btn btn-primary px-4 py-2">
                                    <span>ابدأ التحليل</span>
                                    <i class="fas fa-arrow-left ms-2"></i>
                                </button>
                            </div>
                        </form>

                        <div class="tips-card p-4 mt-5 rounded-4">
                            <h3 class="h5 text-primary fw-bold mb-3">نصائح للحصول على نتائج أفضل:</h3>
                            <ul class="mb-0">
                                <li class="mb-2">كن محدداً في وصف أعراضك</li>
                                <li class="mb-2">اذكر متى بدأت الأعراض</li>
                                <li class="mb-2">اذكر أي تاريخ طبي ذي صلة</li>
                                <li class="mb-2">صف شدة الأعراض</li>
                            </ul>
                        </div>
                    </div>

                    <!-- ميزة جديدة: مرجع الأعراض الشائعة -->
                    <div class="card border-0 shadow-sm rounded-4 mt-5">
                        <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                            <h2 class="h4 fw-bold">مرجع الأعراض الشائعة</h2>
                            <p class="text-muted">انقر على أي عرض لإضافته إلى وصفك</p>
                        </div>
                        <div class="card-body px-4">
                            <div class="d-flex flex-wrap gap-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">صداع</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">حمى</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">سعال</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">إرهاق</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">ضيق في التنفس</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">غثيان</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">دوخة</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">ألم في الصدر</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">ألم في الظهر</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">ألم في المفاصل</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">ألم في البطن</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill symptom-btn">التهاب الحلق</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- شاشة التحميل -->
        <div class="position-fixed top-0 start-0 w-100 h-100 bg-white bg-opacity-90 d-none justify-content-center align-items-center flex-column" id="loadingOverlay" style="z-index: 1050;">
            <div class="loader mb-4"></div>
            <p class="fw-medium">جاري تحليل أعراضك...</p>
            <p class="text-muted small">قد يستغرق هذا بضع ثوانٍ</p>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container py-4">
            <div class="row g-4">
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="About.Html" class="link-light text-decoration-none">عن المشروع</a></li>
                        <li class="mb-2"><a href="diagnosis.html" class="link-light text-decoration-none">التشخيص</a></li>
                        <li class="mb-2"><a href="appointments.html" class="link-light text-decoration-none">حجز موعد</a></li>
                        <li class="mb-2"><a href="contact.html" class="link-light text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">تواصل معنا</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="link-light fs-5" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                        <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>123 شارع الصحة، المدينة الطبية</li>
                    </ul>
                </div>
                <div class="col-md-12 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">اشترك في نشرتنا الإخبارية</h5>
                    <form class="d-flex">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني">
                        <button type="submit" class="btn btn-primary ms-2">اشتراك</button>
                    </form>
                </div>
            </div>
            <hr class="my-4 border-light">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2024 مساعد طبي ذكي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-center text-md-end mt-3 mt-md-0">
                    <small>صُنع بـ <i class="fas fa-heart text-danger"></i> من أجل رعاية صحية أفضل</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Custom JS -->
    <script src="js/main-bootstrap.js"></script>
    <script>
        // Symptom buttons functionality
        document.addEventListener('DOMContentLoaded', function() {
            const symptomButtons = document.querySelectorAll('.symptom-btn');
            const symptomsTextarea = document.getElementById('symptoms');
            
            symptomButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const symptomText = this.textContent;
                    if (symptomsTextarea.value) {
                        symptomsTextarea.value += ", " + symptomText.toLowerCase();
                    } else {
                        symptomsTextarea.value = "I'm experiencing " + symptomText.toLowerCase();
                    }
                    symptomsTextarea.focus();
                });
            });

            // Show loading overlay on form submit
            const symptomForm = document.getElementById('symptomForm');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            if (symptomForm && loadingOverlay) {
                symptomForm.addEventListener('submit', function() {
                    loadingOverlay.classList.remove('d-none');
                    loadingOverlay.classList.add('d-flex');
                });
            }
        });
    </script>
</body>
</html>
